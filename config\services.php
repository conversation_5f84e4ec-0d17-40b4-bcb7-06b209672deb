<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Free Search Services
    |--------------------------------------------------------------------------
    */
    'free_search' => [
        'duckduckgo' => [
            'base_url' => 'https://api.duckduckgo.com',
            'enabled' => true,
        ],
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'deepseek' => [
        'api_key' => env('DEEPSEEK_API_KEY'),
        'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
        'timeout' => env('DEEPSEEK_TIMEOUT', 15), // Reduced from 30s to 15s
        'max_retries' => env('DEEPSEEK_MAX_RETRIES', 3),
        'initial_retry_delay' => env('DEEPSEEK_INITIAL_RETRY_DELAY', 1000), // ms
        'backoff_factor' => env('DEEPSEEK_BACKOFF_FACTOR', 2.0),
        'failure_threshold' => env('DEEPSEEK_FAILURE_THRESHOLD', 5),
        'circuit_cooldown' => env('DEEPSEEK_CIRCUIT_COOLDOWN', 30000), // ms
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Services Configuration (Free alternatives only)
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | Image Generation Services Configuration (Gemini only)
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | Voice Services Configuration (Browser-based only)
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | Document Analysis Services Configuration (DeepSeek only)
    |--------------------------------------------------------------------------
    */

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'base_url' => env('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com'),
        'timeout' => env('GEMINI_TIMEOUT', 20), // Reduced from 30s to 20s
        'max_retries' => env('GEMINI_MAX_RETRIES', 3),
        'initial_retry_delay' => env('GEMINI_INITIAL_RETRY_DELAY', 1000), // ms
        'backoff_factor' => env('GEMINI_BACKOFF_FACTOR', 2.0),
    ],

    /*
    |--------------------------------------------------------------------------
    | LLM Client Configuration
    |--------------------------------------------------------------------------
    */
    'llm_fallback' => [
        'primary' => env('LLM_PRIMARY', 'deepseek'), // deepseek or gemini
        'fallback_enabled' => env('LLM_FALLBACK_ENABLED', true),
    ],

    'llm_client' => App\Services\ApiFallbackService::class,

];
