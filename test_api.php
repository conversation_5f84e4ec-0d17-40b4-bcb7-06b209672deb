<?php

$url = 'http://127.0.0.1:8000/api/chat';
$data = [
    'message' => 'Hello, how are you?',
    'session_id' => 'test-session-123'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Error making request\n";
    print_r($http_response_header);
} else {
    echo "Response:\n";
    echo $result . "\n";
}
