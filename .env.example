APP_NAME="WIDDX AI"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# WIDDX AI Configuration
# DeepSeek API Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_TIMEOUT=30

# Gemini API Configuration (Optional - for backup/comparison)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_BASE_URL=https://generativelanguage.googleapis.com
GEMINI_TIMEOUT=30

# WIDDX Settings
WIDDX_DEFAULT_PERSONALITY=neutral
WIDDX_MAX_MESSAGE_LENGTH=10000
WIDDX_SESSION_CLEANUP_DAYS=30
WIDDX_ENABLE_LOGGING=true

# Live Search API Keys
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
BING_SEARCH_API_KEY=your_bing_search_api_key_here
SERP_API_KEY=your_serp_api_key_here

# Image Generation API Keys
OPENAI_API_KEY=your_openai_api_key_here
STABILITY_API_KEY=your_stability_ai_api_key_here
MIDJOURNEY_API_KEY=your_midjourney_api_key_here
MIDJOURNEY_BASE_URL=your_midjourney_base_url_here

# Voice Services API Keys
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
AZURE_SPEECH_API_KEY=your_azure_speech_api_key_here
AZURE_SPEECH_REGION=your_azure_speech_region_here

# Document Analysis API Keys
AZURE_DOCUMENT_API_KEY=your_azure_document_api_key_here
AZURE_DOCUMENT_ENDPOINT=your_azure_document_endpoint_here
GOOGLE_CLOUD_CREDENTIALS_PATH=path_to_google_cloud_credentials.json
GOOGLE_CLOUD_PROJECT_ID=your_google_cloud_project_id

# Multilingual Support
WIDDX_MULTILINGUAL_ENABLED=true
WIDDX_DEFAULT_LANGUAGE=english
WIDDX_DEFAULT_LANGUAGE_CODE=en
WIDDX_AUTO_DETECT_LANGUAGE=true
WIDDX_LANGUAGE_MIN_CONFIDENCE=0.3
WIDDX_FALLBACK_TO_ENGLISH=true
