<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_session_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['user', 'widdx']); // Only user and WIDDX, never mention other models
            $table->longText('content');
            $table->json('metadata')->nullable(); // Store processing info, model responses, etc.
            $table->string('personality_applied')->nullable();
            $table->float('processing_time')->nullable(); // Time taken to generate response
            $table->timestamps();

            $table->index(['chat_session_id', 'created_at']);
            $table->index('role');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
