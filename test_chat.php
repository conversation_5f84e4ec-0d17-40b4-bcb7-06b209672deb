<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "Testing WIDDX AI Chat Functionality\n";
    echo "===================================\n\n";
    
    // Test DeepSeek Client
    echo "1. Testing DeepSeek Client:\n";
    $deepSeekClient = app(App\Services\DeepSeekClient::class);
    echo "   - Configured: " . ($deepSeekClient->isConfigured() ? "Yes" : "No") . "\n";
    
    // Test Gemini Client
    echo "2. Testing Gemini Client:\n";
    $geminiClient = app(App\Services\GeminiClient::class);
    echo "   - Configured: " . ($geminiClient->isConfigured() ? "Yes" : "No") . "\n";
    
    // Test ModelMergerService
    echo "3. Testing ModelMergerService:\n";
    $modelMerger = app(App\Services\ModelMergerService::class);
    $availableModels = $modelMerger->getAvailableModels();
    echo "   - Available models: " . implode(', ', $availableModels) . "\n";
    
    // Test a simple chat message
    echo "4. Testing Chat Message Processing:\n";
    $response = $modelMerger->processMessage(
        "Hello, how are you?",
        [],
        [
            'personality_prompt' => '',
            'target_language' => 'english',
            'target_language_code' => 'en',
            'language_confidence' => 1.0,
            'max_tokens' => 100,
            'temperature' => 0.7,
            'think_mode' => false,
        ]
    );
    
    echo "   - Success: " . ($response['success'] ? "Yes" : "No") . "\n";
    if ($response['success']) {
        echo "   - Response: " . substr($response['content'], 0, 100) . "...\n";
        echo "   - Processing time: " . round($response['processing_time'], 3) . "s\n";
        echo "   - Models used: " . implode(', ', $response['metadata']['models_used']) . "\n";
    } else {
        echo "   - Error: " . ($response['error'] ?? 'Unknown error') . "\n";
    }
    
    echo "\nTest completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
