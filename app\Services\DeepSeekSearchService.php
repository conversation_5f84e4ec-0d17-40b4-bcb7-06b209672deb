<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class DeepSeekSearchService
{
    private DeepSeekClient $deepSeek;
    private UnlimitedSearchService $unlimitedSearch;
    private FreeSearchService $freeSearch;
    private int $timeout;
    private int $cacheMinutes;

    public function __construct(
        DeepSeekClient $deepSeek,
        UnlimitedSearchService $unlimitedSearch,
        FreeSearchService $freeSearch
    ) {
        $this->deepSeek = $deepSeek;
        $this->unlimitedSearch = $unlimitedSearch;
        $this->freeSearch = $freeSearch;
        $this->timeout = config('services.deepseek.timeout', 30);
        $this->cacheMinutes = config('widdx.features.unlimited_search.cache_duration', 10);
    }

    /**
     * البحث الذكي باستخدام DeepSeek لتحسين الاستعلامات وتحليل النتائج
     */
    public function intelligentSearch(string $query, array $options = []): array
    {
        try {
            $cacheKey = 'deepseek_search_' . md5($query . serialize($options));
            
            // Check cache first
            if ($cached = Cache::get($cacheKey)) {
                Log::info('DeepSeek search cache hit', ['query' => $query]);
                return $this->addDeepSeekMetadata($cached);
            }

            $language = $options['language'] ?? 'ar';
            $maxResults = $options['max_results'] ?? 20;

            Log::info('Starting DeepSeek intelligent search', [
                'query' => $query,
                'language' => $language,
                'max_results' => $maxResults,
            ]);

            // Step 1: تحليل الاستعلام وتوليد استعلامات محسنة
            $enhancedQueries = $this->generateEnhancedQueries($query, $language);

            // Step 2: البحث باستخدام الاستعلامات المحسنة
            $searchResults = $this->performEnhancedSearch($enhancedQueries, $options);

            // Step 3: تحليل وتصنيف النتائج باستخدام DeepSeek
            $analyzedResults = $this->analyzeSearchResults($query, $searchResults, $language);

            // Step 4: توليد ملخص ذكي ونقاط رئيسية
            $intelligentSummary = $this->generateIntelligentSummary($query, $analyzedResults, $language);

            $finalResult = [
                'success' => true,
                'query' => $query,
                'enhanced_queries' => $enhancedQueries,
                'total_results' => count($analyzedResults['results']),
                'results' => $analyzedResults['results'],
                'intelligent_summary' => $intelligentSummary,
                'key_insights' => $analyzedResults['key_insights'],
                'related_topics' => $analyzedResults['related_topics'],
                'search_strategy' => $analyzedResults['search_strategy'],
                'deepseek_powered' => true,
                'unlimited_mode' => true,
                'timestamp' => now()->toISOString(),
            ];

            // Cache the results
            Cache::put($cacheKey, $finalResult, now()->addMinutes($this->cacheMinutes));

            Log::info('DeepSeek intelligent search completed', [
                'query' => $query,
                'total_results' => count($analyzedResults['results']),
                'enhanced_queries_count' => count($enhancedQueries),
            ]);

            return $this->addDeepSeekMetadata($finalResult);

        } catch (\Exception $e) {
            Log::error('DeepSeek intelligent search failed', [
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'query' => $query,
                'results' => [],
                'deepseek_powered' => true,
                'fallback_used' => true,
            ];
        }
    }

    /**
     * توليد استعلامات محسنة باستخدام DeepSeek
     */
    private function generateEnhancedQueries(string $originalQuery, string $language): array
    {
        try {
            $systemPrompt = $language === 'ar' 
                ? "أنت خبير في تحسين استعلامات البحث. مهمتك هي تحليل الاستعلام الأصلي وتوليد استعلامات بحث محسنة ومتنوعة للحصول على أفضل النتائج."
                : "You are an expert in search query optimization. Your task is to analyze the original query and generate enhanced, diverse search queries for better results.";

            $userPrompt = $language === 'ar'
                ? "الاستعلام الأصلي: \"{$originalQuery}\"

قم بتوليد 5-7 استعلامات بحث محسنة ومتنوعة تغطي جوانب مختلفة من الموضوع:
1. استعلام مباشر ومحدد
2. استعلام واسع وشامل  
3. استعلام تقني أو متخصص
4. استعلام للأخبار الحديثة
5. استعلام للمصادر التعليمية
6. استعلام للمقارنات والبدائل
7. استعلام للمشاكل والحلول

أرجع النتيجة كـ JSON array بهذا الشكل:
{\"queries\": [\"استعلام 1\", \"استعلام 2\", ...]}"
                : "Original query: \"{$originalQuery}\"

Generate 5-7 enhanced and diverse search queries covering different aspects:
1. Direct and specific query
2. Broad and comprehensive query
3. Technical or specialized query
4. Recent news query
5. Educational resources query
6. Comparisons and alternatives query
7. Problems and solutions query

Return as JSON array: {\"queries\": [\"query1\", \"query2\", ...]}";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt],
            ], [
                'max_tokens' => 800,
                'temperature' => 0.7,
            ]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\{.*\}/s', $content, $matches)) {
                    $data = json_decode($matches[0], true);
                    if (isset($data['queries']) && is_array($data['queries'])) {
                        return array_merge([$originalQuery], $data['queries']);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::warning('Enhanced query generation failed', ['error' => $e->getMessage()]);
        }

        // Fallback: return original query with basic variations
        return [
            $originalQuery,
            $originalQuery . ' معلومات',
            $originalQuery . ' شرح',
            $originalQuery . ' دليل',
        ];
    }

    /**
     * البحث باستخدام الاستعلامات المحسنة
     */
    private function performEnhancedSearch(array $queries, array $options): array
    {
        $allResults = [];
        $maxResultsPerQuery = max(5, intval(($options['max_results'] ?? 20) / count($queries)));

        foreach ($queries as $query) {
            try {
                // Use unlimited search for better results
                $result = $this->unlimitedSearch->search($query, [
                    'providers' => $options['providers'] ?? ['duckduckgo'],
                    'max_results' => $maxResultsPerQuery,
                    'language' => $options['language'] ?? 'ar',
                    'region' => $options['region'] ?? 'SA',
                ]);

                if ($result['success'] && !empty($result['results'])) {
                    foreach ($result['results'] as $item) {
                        $item['source_query'] = $query;
                        $allResults[] = $item;
                    }
                }

                // Small delay to avoid overwhelming the system
                usleep(200000); // 0.2 seconds

            } catch (\Exception $e) {
                Log::warning('Enhanced search failed for query', [
                    'query' => $query,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $allResults;
    }

    /**
     * تحليل نتائج البحث باستخدام DeepSeek
     */
    private function analyzeSearchResults(string $originalQuery, array $searchResults, string $language): array
    {
        try {
            // Prepare content for analysis
            $resultsText = '';
            $processedResults = [];

            foreach (array_slice($searchResults, 0, 20) as $index => $result) {
                $title = $result['title'] ?? 'No title';
                $snippet = $result['snippet'] ?? 'No description';
                $url = $result['url'] ?? '';
                
                $resultsText .= "نتيجة " . ($index + 1) . ":\n";
                $resultsText .= "العنوان: {$title}\n";
                $resultsText .= "الوصف: {$snippet}\n";
                $resultsText .= "الرابط: {$url}\n\n";

                // Calculate relevance score using DeepSeek
                $relevanceScore = $this->calculateRelevanceScore($originalQuery, $title, $snippet);
                
                $processedResults[] = array_merge($result, [
                    'relevance_score' => $relevanceScore,
                    'analysis_index' => $index + 1,
                ]);
            }

            // Sort by relevance score
            usort($processedResults, function($a, $b) {
                return $b['relevance_score'] <=> $a['relevance_score'];
            });

            // Generate insights using DeepSeek
            $insights = $this->generateSearchInsights($originalQuery, $resultsText, $language);

            return [
                'results' => $processedResults,
                'key_insights' => $insights['key_insights'],
                'related_topics' => $insights['related_topics'],
                'search_strategy' => $insights['search_strategy'],
            ];

        } catch (\Exception $e) {
            Log::error('Search results analysis failed', ['error' => $e->getMessage()]);
            
            return [
                'results' => $searchResults,
                'key_insights' => [],
                'related_topics' => [],
                'search_strategy' => 'تحليل أساسي بدون DeepSeek',
            ];
        }
    }

    /**
     * حساب درجة الصلة باستخدام DeepSeek
     */
    private function calculateRelevanceScore(string $query, string $title, string $snippet): float
    {
        try {
            $prompt = "قيم مدى صلة هذه النتيجة بالاستعلام من 0 إلى 1:

الاستعلام: \"{$query}\"
العنوان: \"{$title}\"
الوصف: \"{$snippet}\"

أرجع رقم فقط بين 0 و 1 (مثال: 0.85)";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'أنت خبير في تقييم صلة نتائج البحث. قيم الصلة برقم بين 0 و 1.'],
                ['role' => 'user', 'content' => $prompt],
            ], [
                'max_tokens' => 50,
                'temperature' => 0.3,
            ]);

            if ($response['success']) {
                $content = trim($response['content']);
                if (preg_match('/(\d+\.?\d*)/', $content, $matches)) {
                    $score = floatval($matches[1]);
                    return min(1.0, max(0.0, $score));
                }
            }

        } catch (\Exception $e) {
            Log::debug('Relevance scoring failed', ['error' => $e->getMessage()]);
        }

        // Fallback: simple keyword matching
        $queryWords = explode(' ', strtolower($query));
        $titleWords = explode(' ', strtolower($title));
        $snippetWords = explode(' ', strtolower($snippet));
        
        $matches = 0;
        $totalWords = count($queryWords);
        
        foreach ($queryWords as $word) {
            if (in_array($word, $titleWords) || in_array($word, $snippetWords)) {
                $matches++;
            }
        }
        
        return $totalWords > 0 ? $matches / $totalWords : 0.5;
    }

    /**
     * توليد رؤى ذكية من نتائج البحث
     */
    private function generateSearchInsights(string $query, string $resultsText, string $language): array
    {
        try {
            $systemPrompt = $language === 'ar'
                ? "أنت محلل خبير في استخراج الرؤى والمعلومات المهمة من نتائج البحث. مهمتك تحليل النتائج وتقديم ملخص ذكي."
                : "You are an expert analyst in extracting insights and important information from search results. Your task is to analyze results and provide intelligent summary.";

            $userPrompt = $language === 'ar'
                ? "الاستعلام الأصلي: \"{$query}\"

نتائج البحث:
{$resultsText}

قم بتحليل هذه النتائج واستخراج:
1. النقاط الرئيسية والمعلومات المهمة
2. المواضيع ذات الصلة
3. استراتيجية البحث المستخدمة

أرجع النتيجة بصيغة JSON:
{
  \"key_insights\": [\"نقطة 1\", \"نقطة 2\", ...],
  \"related_topics\": [\"موضوع 1\", \"موضوع 2\", ...],
  \"search_strategy\": \"وصف الاستراتيجية\"
}"
                : "Original query: \"{$query}\"

Search results:
{$resultsText}

Analyze these results and extract:
1. Key points and important information
2. Related topics
3. Search strategy used

Return as JSON:
{
  \"key_insights\": [\"insight 1\", \"insight 2\", ...],
  \"related_topics\": [\"topic 1\", \"topic 2\", ...],
  \"search_strategy\": \"strategy description\"
}";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt],
            ], [
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\{.*\}/s', $content, $matches)) {
                    $data = json_decode($matches[0], true);
                    if (is_array($data)) {
                        return [
                            'key_insights' => $data['key_insights'] ?? [],
                            'related_topics' => $data['related_topics'] ?? [],
                            'search_strategy' => $data['search_strategy'] ?? 'تحليل شامل للنتائج',
                        ];
                    }
                }
            }

        } catch (\Exception $e) {
            Log::warning('Search insights generation failed', ['error' => $e->getMessage()]);
        }

        return [
            'key_insights' => ['تم جمع معلومات شاملة حول الموضوع المطلوب'],
            'related_topics' => ['مواضيع ذات صلة بالاستعلام'],
            'search_strategy' => 'بحث متعدد المصادر مع تحليل ذكي',
        ];
    }

    /**
     * توليد ملخص ذكي شامل
     */
    private function generateIntelligentSummary(string $query, array $analyzedResults, string $language): array
    {
        try {
            $topResults = array_slice($analyzedResults['results'], 0, 10);
            $summaryText = '';

            foreach ($topResults as $result) {
                $summaryText .= $result['title'] . ': ' . $result['snippet'] . "\n";
            }

            $systemPrompt = $language === 'ar'
                ? "أنت خبير في تلخيص المعلومات وتقديم ملخصات ذكية وشاملة."
                : "You are an expert in summarizing information and providing intelligent, comprehensive summaries.";

            $userPrompt = $language === 'ar'
                ? "بناءً على نتائج البحث التالية للاستعلام \"{$query}\":

{$summaryText}

قدم ملخصاً ذكياً وشاملاً يتضمن:
1. نظرة عامة على الموضوع
2. النقاط الرئيسية
3. التوصيات أو الخطوات التالية

الملخص يجب أن يكون مفيداً وعملياً."
                : "Based on the following search results for query \"{$query}\":

{$summaryText}

Provide an intelligent and comprehensive summary including:
1. Overview of the topic
2. Key points
3. Recommendations or next steps

The summary should be useful and practical.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt],
            ], [
                'max_tokens' => 800,
                'temperature' => 0.7,
            ]);

            if ($response['success']) {
                return [
                    'summary' => $response['content'],
                    'generated_by' => 'DeepSeek AI',
                    'confidence' => 'high',
                ];
            }

        } catch (\Exception $e) {
            Log::warning('Intelligent summary generation failed', ['error' => $e->getMessage()]);
        }

        return [
            'summary' => 'تم جمع معلومات شاملة حول ' . $query . ' من مصادر متعددة.',
            'generated_by' => 'Fallback',
            'confidence' => 'medium',
        ];
    }

    /**
     * إضافة metadata خاص بـ DeepSeek
     */
    private function addDeepSeekMetadata(array $result): array
    {
        $result['deepseek_powered'] = true;
        $result['ai_enhanced'] = true;
        $result['intelligent_analysis'] = true;
        $result['unlimited_search'] = true;
        $result['rate_limited'] = false;
        
        return $result;
    }

    /**
     * الحصول على قدرات البحث الذكي
     */
    public function getCapabilities(): array
    {
        return [
            'deepseek_powered' => true,
            'intelligent_query_enhancement' => true,
            'ai_result_analysis' => true,
            'relevance_scoring' => true,
            'intelligent_summarization' => true,
            'unlimited_requests' => true,
            'rate_limiting' => false,
            'supported_languages' => ['ar', 'en'],
            'features' => [
                'query_optimization' => 'تحسين الاستعلامات باستخدام الذكاء الصناعي',
                'result_analysis' => 'تحليل وتصنيف النتائج ذكياً',
                'relevance_scoring' => 'تقييم صلة النتائج بالاستعلام',
                'intelligent_summary' => 'ملخصات ذكية شاملة',
                'insight_generation' => 'استخراج الرؤى والنقاط المهمة',
                'related_topics' => 'اقتراح مواضيع ذات صلة',
            ],
            'advantages' => [
                'فهم أعمق للاستعلامات',
                'نتائج أكثر دقة وصلة',
                'تحليل ذكي للمحتوى',
                'ملخصات مفيدة وعملية',
                'رؤى واستنتاجات ذكية',
            ],
        ];
    }
}
