<?php

echo "Testing WIDDX AI Search Features\n";
echo "================================\n\n";

// Test unlimited search
echo "1. Testing Unlimited Search:\n";
$url = 'http://127.0.0.1:8000/api/unlimited-search';
$data = [
    'query' => 'artificial intelligence news',
    'max_results' => 5
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making unlimited search request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Results count: " . count($response['results']) . "\n";
        echo "   Providers used: " . implode(', ', $response['providers_used']) . "\n";
    } else {
        echo "   Error: " . $response['error'] . "\n";
    }
}

echo "\n";

// Test DeepSeek intelligent search
echo "2. Testing DeepSeek Intelligent Search:\n";
$url = 'http://127.0.0.1:8000/api/deepseek-search/intelligent';
$data = [
    'query' => 'latest AI developments',
    'language' => 'en',
    'max_results' => 5
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making DeepSeek search request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Results count: " . count($response['results']) . "\n";
        echo "   AI analysis provided: " . ($response['ai_analysis'] ? 'Yes' : 'No') . "\n";
    } else {
        echo "   Error: " . $response['error'] . "\n";
    }
}

echo "\n";

// Test advanced features search
echo "3. Testing Advanced Features Search:\n";
$url = 'http://127.0.0.1:8000/api/features/search';
$data = [
    'query' => 'machine learning',
    'provider' => 'duckduckgo'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "   Error making advanced features search request\n";
    print_r($http_response_header);
} else {
    $response = json_decode($result, true);
    echo "   Success: " . ($response['success'] ? 'Yes' : 'No') . "\n";
    if ($response['success']) {
        echo "   Results count: " . count($response['results']) . "\n";
        echo "   Provider: " . $response['provider'] . "\n";
    } else {
        echo "   Error: " . $response['error'] . "\n";
    }
}

echo "\nSearch testing completed!\n";
