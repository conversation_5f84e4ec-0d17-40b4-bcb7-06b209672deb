<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>WIDDX AI - Advanced Intelligent Assistant</title>

    {{-- Fonts --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {{-- Tailwind CSS CDN for Production --}}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            'primary': '#1d4ed8',
                            'primary-hover': '#1e40af',
                            'secondary': '#7c3aed',
                        },
                        'widdx-bg': {
                            'primary': '#0a0a0a',
                            'secondary': '#111111',
                            'tertiary': '#1a1a1a',
                            'elevated': '#222222',
                            'hover': '#2a2a2a',
                        },
                        'widdx-text': {
                            'primary': '#ffffff',
                            'secondary': '#a1a1aa',
                            'tertiary': '#71717a',
                            'muted': '#52525b',
                        },
                        'widdx-border': {
                            'primary': '#27272a',
                            'secondary': '#3f3f46',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .widdx-app {
            display: flex;
            height: 100vh;
            width: 100vw;
        }

        .widdx-sidebar {
            width: 320px;
            background: #111111;
            border-right: 1px solid #27272a;
            flex-shrink: 0;
            transition: transform 0.3s ease-in-out;
            position: relative;
            z-index: 50;
        }

        .widdx-sidebar.collapsed {
            transform: translateX(-100%);
        }

        .widdx-main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .widdx-gradient-primary {
            background: linear-gradient(135deg, #1d4ed8, #7c3aed);
        }

        .widdx-chat-header {
            background: #111111;
            border-bottom: 1px solid #27272a;
            padding: 1rem;
            flex-shrink: 0;
        }

        .widdx-messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .widdx-input-area {
            background: #111111;
            border-top: 1px solid #27272a;
            padding: 1rem;
            flex-shrink: 0;
        }

        .widdx-message {
            margin-bottom: 1.5rem;
            opacity: 0;
            animation: slideUp 0.3s ease-out forwards;
        }

        .widdx-message-container {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            max-width: 1024px;
            margin: 0 auto;
        }

        .widdx-message-user .widdx-message-container {
            flex-direction: row-reverse;
        }

        .widdx-message-content {
            flex: 1;
            min-width: 0;
        }

        .widdx-message-text {
            background: #222222;
            border: 1px solid #27272a;
            border-radius: 1rem;
            padding: 1rem;
            color: #ffffff;
            line-height: 1.6;
        }

        .widdx-message-user .widdx-message-text {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }

        .widdx-input-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: #222222;
            border: 1px solid #27272a;
            border-radius: 1.5rem;
            padding: 0.75rem;
        }

        .widdx-textarea {
            flex: 1;
            background: transparent;
            border: none;
            outline: none;
            color: #ffffff;
            resize: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
        }

        .widdx-textarea::placeholder {
            color: #52525b;
        }

        .widdx-send-btn {
            width: 2.5rem;
            height: 2.5rem;
            background: #1d4ed8;
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .widdx-send-btn:hover:not(:disabled) {
            background: #1e40af;
            transform: scale(1.1);
        }

        .widdx-send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }





        .widdx-typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .widdx-typing-dot {
            width: 0.5rem;
            height: 0.5rem;
            background: #1d4ed8;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .widdx-typing-dot:nth-child(1) { animation-delay: 0s; }
        .widdx-typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .widdx-typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        @media (max-width: 1024px) {
            .widdx-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                z-index: 50;
            }

            .widdx-sidebar.collapsed {
                transform: translateX(-100%);
            }
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #111111;
        }

        ::-webkit-scrollbar-thumb {
            background: #27272a;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #3f3f46;
        }
    </style>
</head>
<body>
    <div class="widdx-app">
        {{-- Sidebar --}}
        <aside id="widdx-sidebar" class="widdx-sidebar">
            <div class="p-4">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 widdx-gradient-primary rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <h1 class="text-lg font-semibold text-white">WIDDX AI</h1>
                    </div>
                    <button id="sidebar-toggle" class="text-gray-400 hover:text-white lg:hidden">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <button id="new-chat-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg mb-6 transition-colors">
                    New Chat
                </button>

                <div class="space-y-2">
                    <h3 class="text-xs font-medium text-gray-400 uppercase tracking-wider mb-2">Recent Chats</h3>
                    <div class="bg-gray-800 rounded-lg p-3">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-white">Current Chat</span>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">Now</p>
                    </div>
                </div>


            </div>
        </aside>

        {{-- Main Content --}}
        <div class="widdx-main-content">
            {{-- Header --}}
            <header class="widdx-chat-header">
                <div class="flex items-center justify-between">
                    <button id="mobile-sidebar-toggle" class="text-gray-400 hover:text-white lg:hidden">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <div class="flex items-center space-x-3">
                        <div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div>
                            <h1 class="text-lg font-semibold text-white">WIDDX AI</h1>
                            <p class="text-xs text-gray-400" id="chat-status">Online - Ready to help</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-2">
                        <select id="personality-selector" class="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600">
                            @foreach($personalities as $personality)
                                <option value="{{ $personality['name'] }}" {{ $personality['name'] === 'neutral' ? 'selected' : '' }}>
                                    {{ ucfirst($personality['name']) }}
                                </option>
                            @endforeach
                        </select>

                        <button id="think-mode-toggle" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            <span>Deep Think</span>
                        </button>
                    </div>
                </div>
            </header>



            {{-- Messages --}}
            <main class="widdx-messages-container">
                <div id="messages-list">
                    {{-- Welcome Message --}}
                    <div class="text-center py-12">
                        <div class="widdx-gradient-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white font-bold text-2xl">W</span>
                        </div>
                        <h2 class="text-2xl font-bold text-white mb-2">Welcome to WIDDX AI</h2>
                        <p class="text-gray-400 mb-6 max-w-md mx-auto">
                            I'm your advanced intelligent assistant. I can help you with search, image generation, analysis, and many other tasks.
                        </p>
                        <div class="flex flex-wrap justify-center gap-2">
                            <button class="suggestion-btn px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors" data-suggestion="What are your capabilities?">
                                What are your capabilities?
                            </button>
                            <button class="suggestion-btn px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors" data-suggestion="Search for latest news">
                                Search for latest news
                            </button>
                            <button class="suggestion-btn px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors" data-suggestion="Generate a beautiful image">
                                Generate a beautiful image
                            </button>
                        </div>
                    </div>
                </div>

                {{-- Typing Indicator --}}
                <div id="typing-indicator" class="widdx-message hidden">
                    <div class="widdx-message-container">
                        <div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="widdx-typing-indicator">
                                <div class="widdx-typing-dot"></div>
                                <div class="widdx-typing-dot"></div>
                                <div class="widdx-typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            {{-- Input Area --}}
            <footer class="widdx-input-area">
                <form id="chat-form" class="widdx-input-container">
                    <textarea
                        id="message-input"
                        placeholder="Type your message here... (Ctrl+Enter to send)"
                        class="widdx-textarea"
                        rows="1"
                        maxlength="10000"
                    ></textarea>
                    <button type="submit" id="send-button" class="widdx-send-btn" disabled>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </form>
            </footer>
        </div>
    </div>

    <!-- Image Generation Modal -->
    <div id="image-gen-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-white">Generate Image</h3>
                <button id="close-image-modal" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="image-gen-form">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Image Description</label>
                    <textarea id="image-prompt" class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none resize-none" rows="3" placeholder="Describe the image you want to generate..." required></textarea>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Style</label>
                        <select id="image-style" class="w-full p-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none">
                            <option value="natural">Natural</option>
                            <option value="vivid">Vivid</option>
                            <option value="photographic">Photographic</option>
                            <option value="artistic">Artistic</option>
                            <option value="digital-art">Digital Art</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Quality</label>
                        <select id="image-quality" class="w-full p-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none">
                            <option value="standard">Standard</option>
                            <option value="hd">HD</option>
                        </select>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Size</label>
                    <select id="image-size" class="w-full p-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none">
                        <option value="1024x1024">Square (1024x1024)</option>
                        <option value="1792x1024">Landscape (1792x1024)</option>
                        <option value="1024x1792">Portrait (1024x1792)</option>
                    </select>
                </div>

                <div class="flex gap-3">
                    <button type="button" id="cancel-image-gen" class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                        Cancel
                    </button>
                    <button type="submit" id="generate-image-btn" class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        <span class="generate-text">Generate</span>
                        <span class="loading-text hidden">Generating...</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        class WiddxChatProduction {
            constructor() {
                this.sessionId = this.generateSessionId();
                this.currentPersonality = 'neutral';
                this.isTyping = false;
                this.thinkModeEnabled = false;

                this.initializeElements();
                this.bindEvents();
                this.autoResizeTextarea();
                this.setupKeyboardShortcuts();
            }

            initializeElements() {
                this.messagesContainer = document.getElementById('messages-list');
                this.messageInput = document.getElementById('message-input');
                this.sendButton = document.getElementById('send-button');
                this.chatForm = document.getElementById('chat-form');
                this.personalitySelector = document.getElementById('personality-selector');
                this.typingIndicator = document.getElementById('typing-indicator');
                this.sidebar = document.getElementById('widdx-sidebar');
                this.mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
                this.sidebarToggle = document.getElementById('sidebar-toggle');
                this.newChatBtn = document.getElementById('new-chat-btn');
                this.thinkModeToggle = document.getElementById('think-mode-toggle');
                this.chatStatus = document.getElementById('chat-status');
            }

            bindEvents() {
                // Form submission
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));

                // Input events
                this.messageInput.addEventListener('input', () => this.handleInputChange());
                this.messageInput.addEventListener('keydown', (e) => this.handleKeydown(e));

                // Personality change
                this.personalitySelector.addEventListener('change', (e) => this.handlePersonalityChange(e));

                // Sidebar toggles
                this.mobileSidebarToggle?.addEventListener('click', () => this.toggleSidebar());
                this.sidebarToggle?.addEventListener('click', () => this.toggleSidebar());

                // New chat
                this.newChatBtn?.addEventListener('click', () => this.newChat());

                // Think mode toggle
                this.thinkModeToggle?.addEventListener('click', () => this.toggleThinkMode());

                // Suggestion buttons
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('suggestion-btn')) {
                        const suggestion = e.target.dataset.suggestion;
                        this.messageInput.value = suggestion;
                        this.handleInputChange();
                        this.messageInput.focus();
                    }
                });

                // Online/offline status
                window.addEventListener('online', () => this.updateConnectionStatus(true));
                window.addEventListener('offline', () => this.updateConnectionStatus(false));
            }

            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // Ctrl+Enter to send
                    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                        e.preventDefault();
                        this.handleSubmit(e);
                    }

                    // Escape to clear input
                    if (e.key === 'Escape' && this.messageInput === document.activeElement) {
                        this.clearInput();
                    }

                    // Ctrl+B to toggle sidebar
                    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                        e.preventDefault();
                        this.toggleSidebar();
                    }

                    // Ctrl+N for new chat
                    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                        e.preventDefault();
                        this.newChat();
                    }
                });
            }

            autoResizeTextarea() {
                const autoResize = () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                };

                this.messageInput.addEventListener('input', autoResize);
                autoResize();
            }

            handleInputChange() {
                const value = this.messageInput.value.trim();
                this.sendButton.disabled = value.length === 0;
            }

            handleKeydown(e) {
                if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
                    e.preventDefault();
                    this.handleSubmit(e);
                }
            }

            handlePersonalityChange(e) {
                this.currentPersonality = e.target.value;
                this.showNotification(`Personality changed to ${e.target.value}`, 'info');
            }

            toggleSidebar() {
                this.sidebar.classList.toggle('collapsed');
            }

            toggleThinkMode() {
                this.thinkModeEnabled = !this.thinkModeEnabled;

                // Update button appearance
                if (this.thinkModeEnabled) {
                    this.thinkModeToggle.classList.remove('bg-gray-600', 'hover:bg-gray-700');
                    this.thinkModeToggle.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    this.showNotification('Deep Think Mode activated - I will think more deeply about your questions', 'success');
                } else {
                    this.thinkModeToggle.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    this.thinkModeToggle.classList.add('bg-gray-600', 'hover:bg-gray-700');
                    this.showNotification('Deep Think Mode deactivated', 'info');
                }
            }

            showImageGenerationModal() {
                const modal = document.getElementById('image-gen-modal');
                modal.classList.remove('hidden');
                document.getElementById('image-prompt').focus();
            }

            hideImageGenerationModal() {
                const modal = document.getElementById('image-gen-modal');
                modal.classList.add('hidden');
                // Reset form
                document.getElementById('image-gen-form').reset();
            }

            async generateImage() {
                const prompt = document.getElementById('image-prompt').value.trim();
                const style = document.getElementById('image-style').value;
                const quality = document.getElementById('image-quality').value;
                const size = document.getElementById('image-size').value;

                if (!prompt) {
                    this.showNotification('Please enter an image description', 'error');
                    return;
                }

                // Show loading state
                const generateBtn = document.getElementById('generate-image-btn');
                const generateText = generateBtn.querySelector('.generate-text');
                const loadingText = generateBtn.querySelector('.loading-text');

                generateBtn.disabled = true;
                generateText.classList.add('hidden');
                loadingText.classList.remove('hidden');

                try {
                    // Add user message to chat
                    this.addMessage('user', `Generate image: ${prompt} (Style: ${style}, Quality: ${quality}, Size: ${size})`);

                    // Send request to backend
                    const response = await fetch('/api/features/generate-image', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            style: style,
                            quality: quality,
                            size: size,
                            provider: 'gemini'
                        })
                    });

                    const result = await response.json();

                    if (result.success && result.images && result.images.length > 0) {
                        // Display generated image
                        this.displayGeneratedImage(result);
                        this.hideImageGenerationModal();
                        this.showNotification('تم إنشاء الصورة بنجاح!', 'success');
                    } else {
                        // Use Arabic error message if available
                        const errorMessage = result.arabic_error || result.error || 'فشل في توليد الصورة';
                        throw new Error(errorMessage);
                    }

                } catch (error) {
                    console.error('Image generation error:', error);
                    this.showNotification('Failed to generate image: ' + error.message, 'error');
                } finally {
                    // Reset loading state
                    generateBtn.disabled = false;
                    generateText.classList.remove('hidden');
                    loadingText.classList.add('hidden');
                }
            }

            displayGeneratedImage(result) {
                const imageHtml = result.images.map(image => `
                    <div class="generated-image-container mb-4">
                        <img src="${image.url}" alt="Generated Image" class="max-w-full h-auto rounded-lg shadow-lg">
                        <div class="mt-2 text-sm text-gray-400">
                            <p>Size: ${image.file_size ? this.formatFileSize(image.file_size) : 'Unknown'}</p>
                            <p>Type: ${image.mime_type || 'image/png'}</p>
                        </div>
                    </div>
                `).join('');

                const responseText = `تم إنشاء الصورة بنجاح! 🎨\n\nالوصف: ${result.prompt}\nالمزود: ${result.provider}\n\n${imageHtml}`;

                this.addMessage('assistant', responseText);
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            newChat() {
                this.messagesContainer.innerHTML = `
                    <div class="text-center py-12">
                        <div class="widdx-gradient-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white font-bold text-2xl">W</span>
                        </div>
                        <h2 class="text-2xl font-bold text-white mb-2">New Chat</h2>
                        <p class="text-gray-400 mb-6 max-w-md mx-auto">How can I help you today?</p>
                    </div>
                `;
                this.clearInput();
                this.showNotification('New chat started', 'success');
            }

            async handleSubmit(e) {
                e.preventDefault();

                const message = this.messageInput.value.trim();
                if (!message || this.isTyping) return;

                this.addUserMessage(message);
                this.clearInput();
                this.setTyping(true);

                try {
                    const response = await this.sendMessage(message);
                    this.addWiddxMessage(response.message);
                } catch (error) {
                    this.addWiddxMessage('Sorry, there was an error processing your message. Please try again.');
                    console.error('Chat error:', error);
                    this.showNotification('Error sending message', 'error');
                } finally {
                    this.setTyping(false);
                }
            }

            addUserMessage(message) {
                const messageElement = document.createElement('div');
                messageElement.className = 'widdx-message widdx-message-user';
                messageElement.innerHTML = `
                    <div class="widdx-message-container">
                        <div class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="widdx-message-content">
                            <div class="widdx-message-text">${this.escapeHtml(message)}</div>
                        </div>
                    </div>
                `;
                this.messagesContainer.appendChild(messageElement);
                this.scrollToBottom();
            }

            addWiddxMessage(message) {
                const messageElement = document.createElement('div');
                messageElement.className = 'widdx-message widdx-message-ai';
                messageElement.innerHTML = `
                    <div class="widdx-message-container">
                        <div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div class="widdx-message-content">
                            <div class="widdx-message-text">${this.formatMessage(message)}</div>
                        </div>
                    </div>
                `;
                this.messagesContainer.appendChild(messageElement);
                this.scrollToBottom();
            }

            setTyping(typing) {
                this.isTyping = typing;
                if (typing) {
                    this.typingIndicator.classList.remove('hidden');
                } else {
                    this.typingIndicator.classList.add('hidden');
                }
                this.scrollToBottom();
            }

            async sendMessage(message) {
                const requestData = {
                    message: message,
                    session_id: this.sessionId,
                    personality: this.currentPersonality,
                    think_mode: this.thinkModeEnabled
                };

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            }

            clearInput() {
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                this.handleInputChange();
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                }, 100);
            }

            updateConnectionStatus(isOnline) {
                if (this.chatStatus) {
                    this.chatStatus.textContent = isOnline ? 'Online - Ready to help' : 'Offline';
                    this.chatStatus.className = isOnline ? 'text-xs text-green-400' : 'text-xs text-red-400';
                }
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                const colors = {
                    success: 'bg-green-600 text-white',
                    error: 'bg-red-600 text-white',
                    warning: 'bg-yellow-600 text-black',
                    info: 'bg-blue-600 text-white'
                };

                notification.className += ` ${colors[type]}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto remove
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }

            formatMessage(message) {
                // Basic markdown-like formatting
                return message
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code class="bg-gray-700 px-1 py-0.5 rounded text-sm">$1</code>')
                    .replace(/\n/g, '<br>');
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }
        }

        // Initialize the chat when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.widdxChat = new WiddxChatProduction();
            console.log('🚀 WIDDX AI Production Ready!');

            // Image generation modal event listeners
            const imageModal = document.getElementById('image-gen-modal');
            const closeImageModal = document.getElementById('close-image-modal');
            const cancelImageGen = document.getElementById('cancel-image-gen');
            const imageGenForm = document.getElementById('image-gen-form');

            // Close modal events
            closeImageModal?.addEventListener('click', () => window.widdxChat.hideImageGenerationModal());
            cancelImageGen?.addEventListener('click', () => window.widdxChat.hideImageGenerationModal());

            // Close modal when clicking outside
            imageModal?.addEventListener('click', (e) => {
                if (e.target === imageModal) {
                    window.widdxChat.hideImageGenerationModal();
                }
            });

            // Handle form submission
            imageGenForm?.addEventListener('submit', (e) => {
                e.preventDefault();
                window.widdxChat.generateImage();
            });
        });
    </script>
</body>
</html>
